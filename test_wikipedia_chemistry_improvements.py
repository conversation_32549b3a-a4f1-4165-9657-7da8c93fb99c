#!/usr/bin/env python3
"""
Test script to verify Wikipedia service improvements for chemistry image search.
"""

import asyncio
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from services.wikipedia_service import WikipediaService
from models.schemas import WikipediaImage


async def test_chemistry_image_search():
    """Test the improved chemistry image search functionality."""
    
    service = WikipediaService()
    
    # Test cases with chemical formulas and names
    test_queries = [
        "H2O",
        "NaCl", 
        "C6H12O6",
        "sulfuric acid",
        "benzene",
        "caffeine"
    ]
    
    print("Testing improved Wikipedia chemistry image search...")
    print("=" * 60)
    
    for query in test_queries:
        print(f"\n🧪 Testing query: '{query}'")
        print("-" * 40)
        
        try:
            # Test the main search method
            images = await service.search_images(query, limit=3)
            
            print(f"Found {len(images)} images:")
            
            for i, img in enumerate(images, 1):
                print(f"  {i}. {img.caption}")
                print(f"     URL: {img.url}")
                print(f"     Format: {img.format}")
                print(f"     License: {img.license}")
                
                # Check if it's chemistry-related
                wiki_img = WikipediaImage(
                    url=img.url,
                    title=img.caption,
                    description=img.caption,
                    license=img.license,
                    attribution=img.attribution,
                    width=img.width,
                    height=img.height
                )
                
                is_chemistry = service._is_chemistry_related_image(wiki_img, query)
                print(f"     Chemistry-related: {'✅' if is_chemistry else '❌'}")
                print()
                
        except Exception as e:
            print(f"❌ Error testing '{query}': {e}")
            
        print("-" * 40)


async def test_chemistry_detection():
    """Test the chemistry image detection logic."""
    
    service = WikipediaService()
    
    print("\n🔬 Testing chemistry image detection...")
    print("=" * 60)
    
    # Test cases for chemistry detection
    test_cases = [
        {
            "title": "File:Water-3D-balls.png",
            "description": "3D molecular structure of water showing hydrogen and oxygen atoms",
            "query": "H2O",
            "expected": True
        },
        {
            "title": "File:Benzene-aromatic-3D-balls.png", 
            "description": "Ball-and-stick model of benzene molecule",
            "query": "benzene",
            "expected": True
        },
        {
            "title": "File:Random-photo.jpg",
            "description": "A random photo of a landscape",
            "query": "H2O", 
            "expected": False
        },
        {
            "title": "File:Sodium-chloride-3D-ionic.png",
            "description": "Crystal structure of sodium chloride showing ionic bonds",
            "query": "NaCl",
            "expected": True
        }
    ]
    
    for case in test_cases:
        wiki_img = WikipediaImage(
            url="http://example.com/test.png",
            title=case["title"],
            description=case["description"],
            license="CC BY-SA 4.0",
            attribution="Test",
            width=300,
            height=300
        )
        
        result = service._is_chemistry_related_image(wiki_img, case["query"])
        status = "✅" if result == case["expected"] else "❌"
        
        print(f"{status} {case['title']}")
        print(f"   Query: {case['query']}")
        print(f"   Expected: {case['expected']}, Got: {result}")
        print(f"   Description: {case['description']}")
        print()


async def test_search_terms_generation():
    """Test the chemistry search terms generation."""
    
    service = WikipediaService()
    
    print("\n🔍 Testing chemistry search terms generation...")
    print("=" * 60)
    
    test_queries = ["H2O", "benzene", "glucose", "NaCl"]
    
    for query in test_queries:
        terms = service._get_chemistry_search_terms(query)
        print(f"Query: '{query}'")
        print("Generated search terms:")
        for i, term in enumerate(terms, 1):
            print(f"  {i}. {term}")
        print()


async def main():
    """Run all tests."""
    print("🧪 Wikipedia Chemistry Image Search Improvements Test")
    print("=" * 60)
    
    await test_chemistry_detection()
    await test_search_terms_generation()
    await test_chemistry_image_search()
    
    print("\n✅ All tests completed!")


if __name__ == "__main__":
    asyncio.run(main())
