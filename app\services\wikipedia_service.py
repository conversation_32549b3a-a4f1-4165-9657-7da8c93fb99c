"""Wikipedia and Wikimedia Commons integration service."""

import asyncio
import httpx
import structlog
from typing import List, Optional, Dict, Any
from urllib.parse import quote

from app.config import settings
from app.models.schemas import WikipediaImage, MediaImage
from app.utils.query_normalizer import QueryNormalizer
from app.utils.cache import cache_manager

logger = structlog.get_logger()


class WikipediaService:
    """Service for searching images from Wikipedia and Wikimedia Commons."""
    
    def __init__(self):
        self.wikipedia_api = settings.wikipedia_api_url
        self.commons_api = settings.commons_api_url
        self.timeout = settings.request_timeout_seconds
        
    async def _make_request(self, url: str, params: Optional[Dict] = None) -> Optional[Dict[Any, Any]]:
        """Make HTTP request to Wikipedia/Commons API."""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(url, params=params)
                response.raise_for_status()
                return response.json()
        except httpx.HTTPError as e:
            logger.warning("Wikipedia API request failed", url=url, error=str(e))
            return None
        except Exception as e:
            logger.error("Unexpected error in Wikipedia request", url=url, error=str(e))
            return None
    
    async def search_wikipedia_page(self, query: str) -> Optional[str]:
        """
        Search for Wikipedia page title with chemistry focus.

        Args:
            query: Search term

        Returns:
            Page title if found, None otherwise
        """
        # Try chemistry-focused searches first
        search_queries = [
            f"{query} chemistry",
            f"{query} chemical compound",
            f"{query} molecule",
            query  # Original query as fallback
        ]

        url = "https://en.wikipedia.org/w/api.php"

        for search_query in search_queries:
            params = {
                "action": "query",
                "format": "json",
                "list": "search",
                "srsearch": search_query,
                "srlimit": 3  # Get multiple results to choose the best one
            }

            logger.info("Searching Wikipedia page", query=search_query)

            data = await self._make_request(url, params)
            if data and "query" in data and "search" in data["query"]:
                search_results = data["query"]["search"]

                # Look for chemistry-related pages first
                for result in search_results:
                    title = result["title"]
                    snippet = result.get("snippet", "").lower()

                    # Check if this looks like a chemistry page
                    chemistry_indicators = [
                        "chemical", "compound", "molecule", "formula", "element",
                        "acid", "base", "salt", "oxide", "ion", "atom", "bond",
                        "reaction", "synthesis", "organic", "inorganic"
                    ]

                    if any(indicator in snippet for indicator in chemistry_indicators):
                        logger.info("Found chemistry-related Wikipedia page", query=query, title=title)
                        return title

                # If no chemistry-specific page found, return first result
                if search_results:
                    title = search_results[0]["title"]
                    logger.info("Found Wikipedia page", query=query, title=title)
                    return title

        logger.info("No Wikipedia page found", query=query)
        return None
    
    async def get_page_images(self, page_title: str) -> List[WikipediaImage]:
        """
        Get images from a Wikipedia page.
        
        Args:
            page_title: Wikipedia page title
            
        Returns:
            List of WikipediaImage objects
        """
        params = {
            "action": "query",
            "format": "json",
            "titles": page_title,
            "prop": "images",
            "imlimit": 10
        }
        
        url = "https://en.wikipedia.org/w/api.php"
        
        logger.info("Getting Wikipedia page images", page_title=page_title)
        
        data = await self._make_request(url, params)
        if not data or "query" not in data or "pages" not in data["query"]:
            return []
        
        pages = data["query"]["pages"]
        page_id = list(pages.keys())[0]
        
        if page_id == "-1" or "images" not in pages[page_id]:
            return []
        
        images = []
        image_titles = [img["title"] for img in pages[page_id]["images"]]

        # Get image info for each image and filter for chemistry relevance
        chemistry_images = []
        other_images = []

        for img_title in image_titles[:10]:  # Check more images to find chemistry-related ones
            img_info = await self.get_image_info(img_title)
            if img_info:
                # Separate chemistry-related images from others
                if self._is_chemistry_related_image(img_info, page_title):
                    chemistry_images.append(img_info)
                else:
                    other_images.append(img_info)

        # Prioritize chemistry-related images
        images = chemistry_images[:3] + other_images[:2]  # Max 5 images, prefer chemistry ones

        logger.info("Found Wikipedia page images",
                   page_title=page_title,
                   total_count=len(images),
                   chemistry_count=len(chemistry_images))
        return images[:5]
    
    async def get_image_info(self, image_title: str) -> Optional[WikipediaImage]:
        """
        Get detailed information about an image.
        
        Args:
            image_title: Image title (e.g., "File:Example.jpg")
            
        Returns:
            WikipediaImage object if successful, None otherwise
        """
        params = {
            "action": "query",
            "format": "json",
            "titles": image_title,
            "prop": "imageinfo",
            "iiprop": "url|size|extmetadata",
            "iiurlwidth": 300
        }
        
        url = "https://commons.wikimedia.org/w/api.php"
        
        data = await self._make_request(url, params)
        if not data or "query" not in data or "pages" not in data["query"]:
            return None
        
        pages = data["query"]["pages"]
        page_id = list(pages.keys())[0]
        
        if page_id == "-1" or "imageinfo" not in pages[page_id]:
            return None
        
        img_info = pages[page_id]["imageinfo"][0]
        
        # Extract license and attribution from metadata
        license_info = self._extract_license_info(img_info.get("extmetadata", {}))
        
        # Skip if license is not suitable for educational use
        if not self._is_suitable_license(license_info["license"]):
            return None
        
        return WikipediaImage(
            url=img_info["url"],
            title=image_title,
            description=img_info.get("extmetadata", {}).get("ImageDescription", {}).get("value", ""),
            license=license_info["license"],
            attribution=license_info["attribution"],
            width=img_info.get("width"),
            height=img_info.get("height")
        )
    
    def _extract_license_info(self, metadata: Dict) -> Dict[str, str]:
        """Extract license and attribution from image metadata."""
        license_name = "Unknown"
        attribution = "Wikimedia Commons"
        
        if "LicenseShortName" in metadata:
            license_name = metadata["LicenseShortName"]["value"]
        elif "License" in metadata:
            license_name = metadata["License"]["value"]
        
        if "Artist" in metadata:
            attribution = f"Wikimedia Commons - {metadata['Artist']['value']}"
        elif "Attribution" in metadata:
            attribution = metadata["Attribution"]["value"]
        
        return {
            "license": license_name,
            "attribution": attribution
        }
    
    def _is_suitable_license(self, license_name: str) -> bool:
        """Check if license is suitable for educational use."""
        suitable_licenses = [
            "CC0",
            "CC BY",
            "CC BY-SA",
            "CC BY 2.0",
            "CC BY 3.0",
            "CC BY 4.0",
            "CC BY-SA 2.0",
            "CC BY-SA 3.0",
            "CC BY-SA 4.0",
            "Public domain",
            "PD"
        ]

        license_lower = license_name.lower()
        return any(suitable.lower() in license_lower for suitable in suitable_licenses)

    def _is_chemistry_related_image(self, image: WikipediaImage, query: str) -> bool:
        """
        Check if an image is related to chemistry based on title, description and query.

        Args:
            image: WikipediaImage object
            query: Original search query

        Returns:
            True if image appears to be chemistry-related
        """
        chemistry_keywords = [
            "molecule", "molecular", "structure", "chemical", "chemistry",
            "compound", "formula", "reaction", "bond", "atom", "element",
            "periodic", "table", "crystal", "lattice", "orbital", "electron",
            "ion", "ionic", "covalent", "acid", "base", "salt", "oxide",
            "synthesis", "catalyst", "polymer", "organic", "inorganic",
            "titration", "ph", "buffer", "solution", "concentration"
        ]

        # Keywords that suggest non-chemistry content
        non_chemistry_keywords = [
            "nebula", "galaxy", "star", "planet", "space", "astronomy",
            "logo", "commons", "wikipedia", "landscape", "building",
            "person", "people", "portrait", "biography", "history",
            "geography", "map", "flag", "politics", "economics"
        ]

        # Check title and description for chemistry keywords
        title_lower = image.title.lower() if image.title else ""
        desc_lower = image.description.lower() if image.description else ""
        query_lower = query.lower()
        text_to_check = f"{title_lower} {desc_lower}"

        # Exclude obvious non-chemistry content
        non_chemistry_score = sum(1 for keyword in non_chemistry_keywords if keyword in text_to_check)
        if non_chemistry_score >= 2:  # Strong indication it's not chemistry
            return False

        # High priority: Contains query term in chemistry context
        if query_lower in title_lower or query_lower in desc_lower:
            # But make sure it's in a chemistry context
            chemistry_score = sum(1 for keyword in chemistry_keywords if keyword in text_to_check)
            if chemistry_score >= 1 or non_chemistry_score == 0:
                return True

        # Medium priority: Contains multiple chemistry keywords
        chemistry_score = sum(1 for keyword in chemistry_keywords if keyword in text_to_check)
        if chemistry_score >= 2:  # Require at least 2 chemistry keywords
            return True

        # Low priority: File name suggests chemistry (e.g., molecular diagrams)
        chemistry_file_patterns = [
            "molecular", "structure", "formula", "diagram", "model",
            "3d", "ball", "stick", "wireframe", "space-filling", "vdw"
        ]

        file_score = sum(1 for pattern in chemistry_file_patterns if pattern in title_lower)
        if file_score >= 1 and chemistry_score >= 1:  # File pattern + at least 1 chemistry keyword
            return True

        return False
    
    async def search_commons_images(self, query: str, limit: int = 5) -> List[WikipediaImage]:
        """
        Search for images directly on Wikimedia Commons with chemistry-focused keywords.

        Args:
            query: Search term
            limit: Maximum number of images to return

        Returns:
            List of WikipediaImage objects
        """
        # Try multiple search strategies with chemistry-focused keywords
        search_queries = [
            f"filetype:bitmap|drawing|multimedia {query} molecule",
            f"filetype:bitmap|drawing|multimedia {query} chemical structure",
            f"filetype:bitmap|drawing|multimedia {query} molecular",
            f"filetype:bitmap|drawing|multimedia {query} chemistry",
            f"filetype:bitmap|drawing|multimedia {query}"  # Fallback
        ]

        all_images = []
        seen_titles = set()

        for search_query in search_queries:
            if len(all_images) >= limit:
                break

            params = {
                "action": "query",
                "format": "json",
                "list": "search",
                "srnamespace": 6,  # File namespace
                "srsearch": search_query,
                "srlimit": limit * 2  # Get more to filter out unsuitable ones
            }

            url = self.commons_api

            logger.info("Searching Wikimedia Commons", query=search_query)

            data = await self._make_request(url, params)
            if not data or "query" not in data or "search" not in data["query"]:
                continue

            search_results = data["query"]["search"]

            for result in search_results:
                if len(all_images) >= limit or result["title"] in seen_titles:
                    continue

                seen_titles.add(result["title"])
                img_info = await self.get_image_info(result["title"])

                if img_info and self._is_chemistry_related_image(img_info, query):
                    all_images.append(img_info)

        logger.info("Found Commons images", query=query, count=len(all_images))
        return all_images[:limit]

    async def search_commons_gifs(self, query: str, limit: int = 3) -> List[WikipediaImage]:
        """
        Search for chemistry-related GIF animations on Wikimedia Commons.

        Args:
            query: Search term
            limit: Maximum number of GIFs to return

        Returns:
            List of WikipediaImage objects
        """
        # Search for chemistry-focused GIF files
        gif_search_queries = [
            f"{query} molecule animation .gif",
            f"{query} molecular motion .gif",
            f"{query} reaction .gif",
            f"{query} structure .gif",
            f"{query} .gif"  # Fallback
        ]

        all_gifs = []
        seen_titles = set()

        for search_query in gif_search_queries:
            if len(all_gifs) >= limit:
                break

            params = {
                "action": "query",
                "format": "json",
                "list": "search",
                "srnamespace": 6,  # File namespace
                "srsearch": search_query,
                "srlimit": limit * 2  # Get more to filter for actual GIFs
            }

            url = self.commons_api

            logger.info("Searching Wikimedia Commons for GIFs", query=search_query)

            data = await self._make_request(url, params)
            if not data or "query" not in data or "search" not in data["query"]:
                continue

            search_results = data["query"]["search"]

            for result in search_results:
                if len(all_gifs) >= limit or result["title"] in seen_titles:
                    continue

                seen_titles.add(result["title"])

                # Process all results and check if they're actually GIFs
                img_info = await self.get_image_info(result["title"])
                if (img_info and img_info.url.lower().endswith('.gif') and
                    self._is_chemistry_related_image(img_info, query)):
                    all_gifs.append(img_info)

        logger.info("Found Commons GIFs", query=query, count=len(all_gifs))
        return all_gifs[:limit]
    
    async def search_images(self, query: str, limit: int = 5) -> List[MediaImage]:
        """
        Search for images using both Wikipedia pages and Commons search.

        Args:
            query: Chemical name or formula
            limit: Maximum number of images to return

        Returns:
            List of MediaImage objects
        """
        # Check cache first
        cached_images = await cache_manager.get_images_cache(query, "wikipedia")
        if cached_images:
            logger.info("Found Wikipedia images in cache", query=query, count=len(cached_images))
            return [MediaImage(**img) for img in cached_images[:limit]]

        # Get query variations with chemistry focus
        primary_query, alternatives = QueryNormalizer.normalize_query(query)

        # Add chemistry-specific variations
        chemistry_variations = []
        if QueryNormalizer.is_chemical_formula(query):
            # For chemical formulas, try to find the compound name
            chemistry_variations.extend([
                f"{primary_query} compound",
                f"{primary_query} molecule",
                f"{primary_query} structure"
            ])

        all_queries = [primary_query] + alternatives + chemistry_variations

        all_images = []

        # Search Wikipedia pages first with chemistry focus
        for q in all_queries[:4]:  # Try a few more variations
            page_title = await self.search_wikipedia_page(q)
            if page_title:
                page_images = await self.get_page_images(page_title)
                all_images.extend(page_images)
                if page_images:  # Found relevant images, no need to try more queries
                    break
        
        # Search Commons directly for static images
        commons_images = await self.search_commons_images(primary_query, limit // 2)
        all_images.extend(commons_images)

        # Search Commons for GIF animations
        try:
            print(f"DEBUG: Searching for GIFs with query: {primary_query}")
            gif_images = await self.search_commons_gifs(primary_query, limit // 2)
            print(f"DEBUG: Found {len(gif_images)} GIFs")
            all_images.extend(gif_images)
            logger.info("Added GIF images", count=len(gif_images))
        except Exception as e:
            print(f"DEBUG: GIF search failed: {e}")
            logger.error("Failed to search Commons GIFs", error=str(e))
        
        # Convert to MediaImage format and remove duplicates
        media_images = []
        seen_urls = set()

        print(f"DEBUG: Converting {len(all_images)} images to MediaImage format")
        for i, img in enumerate(all_images):
            print(f"DEBUG: Image {i+1}: {img.url} (format: {self._get_image_format(img.url)})")
            if img.url not in seen_urls and len(media_images) < limit:
                seen_urls.add(img.url)
                
                media_images.append(MediaImage(
                    url=img.url,
                    thumbnail=img.url,  # Use same URL as thumbnail
                    format=self._get_image_format(img.url),
                    width=img.width,
                    height=img.height,
                    source="wikipedia",
                    license=img.license,
                    attribution=img.attribution,
                    caption=img.description or f"Hình ảnh minh họa {query}"
                ))
        
        # Cache the results
        if media_images:
            await cache_manager.set_images_cache(
                query, "wikipedia", [img.model_dump() for img in media_images]
            )

        logger.info("Total Wikipedia/Commons images found", query=query, count=len(media_images))
        return media_images
    
    def _get_chemistry_search_terms(self, query: str) -> List[str]:
        """
        Generate chemistry-specific search terms based on the query.

        Args:
            query: Original search query

        Returns:
            List of chemistry-focused search terms
        """
        base_terms = [query]

        # Common chemistry terms to add
        chemistry_suffixes = [
            "molecule", "molecular structure", "chemical structure",
            "compound", "formula", "3D model", "ball and stick",
            "space filling model", "molecular model"
        ]

        # Add chemistry-specific terms
        for suffix in chemistry_suffixes:
            base_terms.append(f"{query} {suffix}")

        # For chemical formulas, add specific search patterns
        if QueryNormalizer.is_chemical_formula(query):
            base_terms.extend([
                f"{query} molecular geometry",
                f"{query} crystal structure",
                f"{query} electron density",
                f"{query} orbital"
            ])

        return base_terms[:8]  # Limit to avoid too many requests

    def _get_image_format(self, url: str) -> str:
        """Extract image format from URL."""
        if url.lower().endswith('.png'):
            return "png"
        elif url.lower().endswith('.jpg') or url.lower().endswith('.jpeg'):
            return "jpg"
        elif url.lower().endswith('.svg'):
            return "svg"
        elif url.lower().endswith('.gif'):
            return "gif"
        else:
            return "unknown"
