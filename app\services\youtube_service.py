"""YouTube Data API integration service."""

import asyncio
import httpx
import structlog
from typing import List, Optional, Dict, Any
from urllib.parse import quote

from app.config import settings
from app.models.schemas import YouTubeVideo, MediaVideo
from app.utils.query_normalizer import QueryNormalizer
from app.utils.cache import cache_manager

logger = structlog.get_logger()


class YouTubeService:
    """Service for searching educational videos on YouTube."""
    
    def __init__(self):
        self.api_url = settings.youtube_api_url
        self.api_key = settings.youtube_api_key
        self.timeout = settings.request_timeout_seconds
        self.educational_channels = settings.youtube_educational_channels
        
    async def _make_request(self, endpoint: str, params: Dict) -> Optional[Dict[Any, Any]]:
        """Make HTTP request to YouTube Data API."""
        if not self.api_key:
            logger.warning("YouTube API key not configured")
            return None
            
        params["key"] = self.api_key
        url = f"{self.api_url}/{endpoint}"
        
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(url, params=params)
                response.raise_for_status()
                return response.json()
        except httpx.HTTPError as e:
            logger.warning("YouTube API request failed", url=url, error=str(e))
            return None
        except Exception as e:
            logger.error("Unexpected error in YouTube request", url=url, error=str(e))
            return None
    
    async def search_videos(self, query: str, max_results: int = 10, region_code: str = None, relevance_language: str = None) -> List[YouTubeVideo]:
        """
        Search for videos on YouTube with language and region support.

        Args:
            query: Search query
            max_results: Maximum number of results
            region_code: Region code (e.g., 'VN' for Vietnam, 'US' for United States)
            relevance_language: Language code (e.g., 'vi' for Vietnamese, 'en' for English)

        Returns:
            List of YouTubeVideo objects
        """
        params = {
            "part": "snippet",
            "q": query,
            "type": "video",
            "maxResults": min(max_results, 25),  # YouTube API limit
            "order": "relevance",
            "videoDuration": "medium",  # 4-20 minutes
            "videoDefinition": "any",
            "videoEmbeddable": "true",
            "safeSearch": "strict"
        }

        # Add language and region parameters if specified
        if region_code:
            params["regionCode"] = region_code
        if relevance_language:
            params["relevanceLanguage"] = relevance_language
        
        logger.info("Searching YouTube videos", query=query, max_results=max_results)
        
        data = await self._make_request("search", params)
        if not data or "items" not in data:
            return []
        
        videos = []
        video_ids = []
        
        for item in data["items"]:
            video_id = item["id"]["videoId"]
            video_ids.append(video_id)
            
            snippet = item["snippet"]
            
            videos.append(YouTubeVideo(
                video_id=video_id,
                title=snippet["title"],
                channel_title=snippet["channelTitle"],
                duration="",  # Will be filled by get_video_details
                thumbnail_url=snippet["thumbnails"]["high"]["url"],
                description=snippet.get("description", ""),
                published_at=snippet["publishedAt"]
            ))
        
        # Get additional details (duration, etc.)
        if video_ids:
            detailed_videos = await self.get_video_details(video_ids)
            # Merge duration info
            for i, video in enumerate(videos):
                if i < len(detailed_videos) and detailed_videos[i]:
                    video.duration = detailed_videos[i].duration
        
        logger.info("Found YouTube videos", query=query, count=len(videos))
        return videos
    
    async def get_video_details(self, video_ids: List[str]) -> List[Optional[YouTubeVideo]]:
        """
        Get detailed information for videos.
        
        Args:
            video_ids: List of YouTube video IDs
            
        Returns:
            List of YouTubeVideo objects with detailed info
        """
        if not video_ids:
            return []
        
        params = {
            "part": "contentDetails,snippet",
            "id": ",".join(video_ids)
        }
        
        data = await self._make_request("videos", params)
        if not data or "items" not in data:
            return [None] * len(video_ids)
        
        videos = []
        for item in data["items"]:
            snippet = item["snippet"]
            content_details = item["contentDetails"]
            
            videos.append(YouTubeVideo(
                video_id=item["id"],
                title=snippet["title"],
                channel_title=snippet["channelTitle"],
                duration=content_details["duration"],
                thumbnail_url=snippet["thumbnails"]["high"]["url"],
                description=snippet.get("description", ""),
                published_at=snippet["publishedAt"]
            ))
        
        return videos
    
    def _is_educational_channel(self, channel_title: str) -> bool:
        """Check if channel is in educational whitelist or has educational indicators."""
        channel_lower = channel_title.lower()

        # Check configured educational channels
        if any(edu_channel.lower() in channel_lower for edu_channel in self.educational_channels):
            return True

        # Check for educational indicators in channel name (English)
        english_educational_indicators = [
            "education", "learning", "science", "chemistry", "tutorial",
            "academy", "university", "college", "school", "lab", "laboratory",
            "mit", "opencourseware", "scishow", "crash course", "khan",
            "professor", "explains", "physics", "biology", "math", "course"
        ]

        # Check for educational indicators in channel name (Vietnamese)
        vietnamese_educational_indicators = [
            "giáo dục", "học tập", "khoa học", "hóa học", "bài học",
            "học viện", "đại học", "trường", "phòng thí nghiệm", "kiến thức"
        ]

        all_indicators = english_educational_indicators + vietnamese_educational_indicators

        return any(indicator in channel_lower for indicator in all_indicators)
    
    def _calculate_video_score(self, video: YouTubeVideo, query: str) -> float:
        """Calculate relevance score for video with Vietnamese language support."""
        score = 0.0

        # Educational channel bonus
        if self._is_educational_channel(video.channel_title):
            score += 2.0

        # Title and description relevance
        title_lower = video.title.lower()
        desc_lower = video.description.lower()
        query_lower = query.lower()

        # Query term in title or description
        if query_lower in title_lower:
            score += 1.5
        elif query_lower in desc_lower:
            score += 1.0

        # Educational keywords in title/description (English)
        english_educational_keywords = [
            "chemistry", "experiment", "reaction", "synthesis",
            "laboratory", "demo", "tutorial", "lesson", "education",
            "molecule", "compound", "formula", "properties", "analysis"
        ]

        # Educational keywords in Vietnamese
        vietnamese_educational_keywords = [
            "hóa học", "thí nghiệm", "phản ứng", "tổng hợp",
            "phòng thí nghiệm", "thực hành", "bài học", "giáo dục",
            "phân tử", "hợp chất", "công thức", "tính chất", "phân tích",
            "giải thích", "học tập", "kiến thức", "khoa học"
        ]

        # Check for educational keywords
        text_to_check = f"{title_lower} {desc_lower}"

        for keyword in english_educational_keywords:
            if keyword in text_to_check:
                score += 0.5

        for keyword in vietnamese_educational_keywords:
            if keyword in text_to_check:
                score += 0.6  # Slight bonus for Vietnamese educational content

        # Avoid non-educational content (English)
        avoid_keywords_english = [
            "music", "song", "funny", "prank", "meme", "gaming",
            "entertainment", "comedy", "vlog", "unboxing"
        ]

        # Avoid non-educational content (Vietnamese)
        avoid_keywords_vietnamese = [
            "nhạc", "bài hát", "hài", "giải trí", "vui nhộn",
            "game", "chơi", "vlog", "review", "mở hộp"
        ]

        for keyword in avoid_keywords_english + avoid_keywords_vietnamese:
            if keyword in text_to_check:
                score -= 1.0

        return max(0.0, score)

    def _create_localized_queries(self, query: str) -> Dict[str, List[str]]:
        """
        Create localized search queries for different languages.

        Args:
            query: Base chemical query

        Returns:
            Dictionary with 'vietnamese' and 'english' query lists
        """
        # Vietnamese educational terms
        vietnamese_terms = [
            "hóa học", "thí nghiệm", "phản ứng", "tính chất", "phân tử",
            "bài học", "giải thích", "thực hành", "phòng thí nghiệm"
        ]

        # English educational terms
        english_terms = [
            "chemistry", "experiment", "reaction", "properties", "molecule",
            "lesson", "explanation", "laboratory", "demo", "tutorial"
        ]

        vietnamese_queries = []
        english_queries = []

        # Create Vietnamese queries
        for term in vietnamese_terms[:5]:  # Limit to avoid too many queries
            vietnamese_queries.extend([
                f"{query} {term}",
                f"{term} {query}"
            ])

        # Create English queries
        for term in english_terms[:5]:  # Limit to avoid too many queries
            english_queries.extend([
                f"{query} {term}",
                f"{term} {query}"
            ])

        return {
            "vietnamese": vietnamese_queries[:8],  # Limit results
            "english": english_queries[:8]
        }
    
    async def search_educational_videos(self, query: str, limit: int = 5) -> List[MediaVideo]:
        """
        Search for educational videos about a chemical compound.

        Args:
            query: Chemical name or formula
            limit: Maximum number of videos to return

        Returns:
            List of MediaVideo objects
        """
        if not self.api_key:
            logger.warning("YouTube API key not configured, skipping video search")
            return []

        # Check cache first
        cached_videos = await cache_manager.get_videos_cache(query)
        if cached_videos:
            logger.info("Found YouTube videos in cache", query=query, count=len(cached_videos))
            return [MediaVideo(**video) for video in cached_videos[:limit]]

        # Expand query for YouTube search
        expanded_queries = QueryNormalizer.expand_query_for_search(query, "youtube")

        all_videos = []

        # Search with different query variations and languages
        # First, search for Vietnamese content
        vietnamese_queries = [q for q in expanded_queries if any(vn_word in q for vn_word in ["hóa học", "thí nghiệm", "phản ứng", "phân tử"])]
        english_queries = [q for q in expanded_queries if q not in vietnamese_queries]

        # Search Vietnamese content first (prioritize local content)
        for search_query in vietnamese_queries[:2]:  # Limit API calls
            videos = await self.search_videos(search_query, max_results=8, region_code="VN", relevance_language="vi")
            all_videos.extend(videos)

        # Then search English content
        for search_query in english_queries[:2]:  # Limit API calls
            videos = await self.search_videos(search_query, max_results=8, relevance_language="en")
            all_videos.extend(videos)

        # If we don't have enough videos, search with general queries
        if len(all_videos) < 10:
            for search_query in expanded_queries[:2]:
                videos = await self.search_videos(search_query, max_results=6)
                all_videos.extend(videos)
        
        # Score and sort videos
        scored_videos = []
        for video in all_videos:
            score = self._calculate_video_score(video, query)
            scored_videos.append((score, video))
        
        # Sort by score (descending) and remove duplicates
        scored_videos.sort(key=lambda x: x[0], reverse=True)
        
        seen_video_ids = set()
        unique_videos = []
        
        for score, video in scored_videos:
            if video.video_id not in seen_video_ids and len(unique_videos) < limit:
                seen_video_ids.add(video.video_id)
                unique_videos.append(video)
        
        # Convert to MediaVideo format
        media_videos = []
        for video in unique_videos:
            media_videos.append(MediaVideo(
                video_id=video.video_id,
                url=f"https://www.youtube.com/watch?v={video.video_id}",
                title=video.title,
                channel=video.channel_title,
                duration=video.duration,
                thumbnails=[video.thumbnail_url],
                source="youtube",
                license="YouTube Standard License"
            ))
        
        # Cache the results
        if media_videos:
            await cache_manager.set_videos_cache(
                query, [video.model_dump() for video in media_videos]
            )

        logger.info("Found educational YouTube videos", query=query, count=len(media_videos))
        return media_videos
