"""Configuration settings for Chemistry Media API."""

from pydantic_settings import BaseSettings
from typing import List, Optional


class Settings(BaseSettings):
    """Application settings."""
    
    # API Configuration
    app_name: str = "Chemistry Media API"
    app_version: str = "1.0.0"
    debug: bool = False
    
    # Server Configuration
    host: str = "0.0.0.0"
    port: int = 8000
    
    # External API Keys
    youtube_api_key: Optional[str] = None
    chemspider_api_key: Optional[str] = None
    
    # Cache Configuration
    redis_url: str = "redis://localhost:6379"
    cache_ttl_seconds: int = 86400  # 1 day
    
    # Request Configuration
    request_timeout_seconds: int = 10
    max_concurrent_requests: int = 5
    
    # Default Limits
    default_image_limit: int = 6
    default_video_limit: int = 6
    max_total_limit: int = 100
    
    # ChemSpider Configuration
    chemspider_base_url: str = "https://api.rsc.org/compounds/v1"
    chemspider_image_width: int = 400
    chemspider_image_height: int = 400
    
    # Wikipedia Configuration
    wikipedia_api_url: str = "https://en.wikipedia.org/api/rest_v1"
    commons_api_url: str = "https://commons.wikimedia.org/w/api.php"
    
    # YouTube Configuration
    youtube_api_url: str = "https://www.googleapis.com/youtube/v3"
    youtube_search_duration: List[str] = ["short", "medium"]
    youtube_educational_channels: List[str] = [
        # English educational channels
        "Khan Academy",
        "Crash Course",
        "Professor Dave Explains",
        "The Organic Chemistry Tutor",
        "MIT OpenCourseWare",
        "SciShow",
        "Veritasium",
        "3Blue1Brown",
        "TED-Ed",

        # Vietnamese educational channels
        "Hóa học 12",
        "Khoa học giáo dục",
        "Đại học Bách Khoa",
        "Học viện Công nghệ",
        "Phòng thí nghiệm Hóa học",
        "Giáo dục Việt Nam",
        "Khoa học tự nhiên",
        "Bài học hóa học",
        "Thí nghiệm khoa học"
    ]

    # Authentication Configuration
    secret_key: str = "your-secret-key-change-this-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 60  # 1 hour

    # MongoDB Configuration
    mongodb_url: str = "mongodb://localhost:27017"
    mongodb_database: str = "chemistry_api"

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# Global settings instance
settings = Settings()
