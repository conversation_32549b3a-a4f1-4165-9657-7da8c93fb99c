#!/usr/bin/env python3
"""
Comprehensive test for both Wikipedia and YouTube Vietnamese improvements.
"""

import asyncio
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from services.wikipedia_service import WikipediaService
from services.youtube_service import YouTubeService
from utils.query_normalizer import QueryNormalizer


def test_query_normalization_comprehensive():
    """Test comprehensive query normalization for both services."""
    
    print("🔍 Testing comprehensive query normalization...")
    print("=" * 70)
    
    test_queries = [
        "H2O",
        "NaCl", 
        "C6H12O6",
        "axit sunfuric",  # Vietnamese input
        "muối ăn",        # Vietnamese input
        "glucose"
    ]
    
    for query in test_queries:
        print(f"\nQuery: '{query}'")
        print("-" * 40)
        
        # Test basic normalization
        primary, alternatives = QueryNormalizer.normalize_query(query)
        print(f"Primary: {primary}")
        print(f"Alternatives: {alternatives}")
        
        # Test YouTube expansion
        youtube_queries = QueryNormalizer.expand_query_for_search(query, "youtube")
        vietnamese_yt = [q for q in youtube_queries if any(vn in q for vn in ["hóa học", "thí nghiệm", "phản ứng"])]
        english_yt = [q for q in youtube_queries if q not in vietnamese_yt]
        
        print(f"YouTube queries: {len(youtube_queries)} total")
        print(f"  - Vietnamese: {len(vietnamese_yt)}")
        print(f"  - English: {len(english_yt)}")
        
        # Test Wikipedia expansion  
        wikipedia_queries = QueryNormalizer.expand_query_for_search(query, "wikipedia")
        print(f"Wikipedia queries: {len(wikipedia_queries)}")


def test_chemistry_detection_comprehensive():
    """Test chemistry detection across both services."""
    
    print("\n🧪 Testing chemistry detection across services...")
    print("=" * 70)
    
    wikipedia_service = WikipediaService()
    youtube_service = YouTubeService()
    
    # Test chemistry search terms generation
    test_queries = ["H2O", "benzene", "NaCl"]
    
    for query in test_queries:
        print(f"\nQuery: '{query}'")
        
        # Test Wikipedia chemistry search terms
        wiki_terms = wikipedia_service._get_chemistry_search_terms(query)
        print(f"Wikipedia chemistry terms ({len(wiki_terms)}):")
        for i, term in enumerate(wiki_terms[:3], 1):
            print(f"  {i}. {term}")
        
        # Test YouTube localized queries
        yt_localized = youtube_service._create_localized_queries(query)
        print(f"YouTube Vietnamese queries ({len(yt_localized['vietnamese'])}):")
        for i, term in enumerate(yt_localized['vietnamese'][:3], 1):
            print(f"  {i}. {term}")
        
        print(f"YouTube English queries ({len(yt_localized['english'])}):")
        for i, term in enumerate(yt_localized['english'][:3], 1):
            print(f"  {i}. {term}")


def test_educational_content_scoring():
    """Test educational content scoring and filtering."""
    
    print("\n📚 Testing educational content scoring...")
    print("=" * 70)
    
    youtube_service = YouTubeService()
    
    # Test educational channel detection with mixed languages
    test_channels = [
        ("Khan Academy", "English", True),
        ("Hóa học 12", "Vietnamese", True), 
        ("MIT OpenCourseWare", "English", True),
        ("Khoa học giáo dục", "Vietnamese", True),
        ("Music Remix", "Mixed", False),
        ("Gaming Channel", "English", False)
    ]
    
    print("Educational channel detection:")
    for channel, language, expected in test_channels:
        result = youtube_service._is_educational_channel(channel)
        status = "✅" if result == expected else "❌"
        print(f"{status} {channel} ({language}): {result}")


def test_language_prioritization():
    """Test language prioritization in search strategies."""
    
    print("\n🌐 Testing language prioritization...")
    print("=" * 70)
    
    # Test query categorization for search prioritization
    test_queries = ["H2O", "glucose", "sulfuric acid"]
    
    for query in test_queries:
        expanded = QueryNormalizer.expand_query_for_search(query, "youtube")
        
        vietnamese_queries = [q for q in expanded if any(vn_word in q for vn_word in ["hóa học", "thí nghiệm", "phản ứng", "phân tử"])]
        english_queries = [q for q in expanded if q not in vietnamese_queries]
        
        print(f"\nQuery: '{query}'")
        print(f"Search strategy:")
        print(f"  1. Vietnamese content: {len(vietnamese_queries)} queries (region=VN, lang=vi)")
        print(f"  2. English content: {len(english_queries)} queries (lang=en)")
        print(f"  3. Fallback: General search if needed")
        
        # Show sample queries
        if vietnamese_queries:
            print(f"  Sample Vietnamese: '{vietnamese_queries[0]}'")
        if english_queries:
            print(f"  Sample English: '{english_queries[0]}'")


def test_content_filtering():
    """Test content filtering for chemistry relevance."""
    
    print("\n🔬 Testing content filtering for chemistry relevance...")
    print("=" * 70)
    
    wikipedia_service = WikipediaService()
    
    # Mock image data for testing
    from models.schemas import WikipediaImage
    
    test_images = [
        {
            "title": "File:Water-molecule-3D.png",
            "description": "3D molecular structure of water H2O showing bonds",
            "query": "H2O",
            "expected": True,
            "reason": "Chemistry content with molecular structure"
        },
        {
            "title": "File:Crab-Nebula.jpg", 
            "description": "Astronomical image of nebula in space",
            "query": "H2O",
            "expected": False,
            "reason": "Astronomy content, not chemistry"
        },
        {
            "title": "File:Sodium-chloride-crystal.png",
            "description": "Crystal structure of NaCl showing ionic bonds",
            "query": "NaCl", 
            "expected": True,
            "reason": "Chemistry content with crystal structure"
        },
        {
            "title": "File:Logo-commons.svg",
            "description": "Wikimedia Commons logo",
            "query": "benzene",
            "expected": False,
            "reason": "Logo, not educational content"
        }
    ]
    
    print("Chemistry relevance filtering:")
    for test_case in test_images:
        wiki_img = WikipediaImage(
            url="http://example.com/test.png",
            title=test_case["title"],
            description=test_case["description"],
            license="CC BY-SA 4.0",
            attribution="Test",
            width=300,
            height=300
        )
        
        result = wikipedia_service._is_chemistry_related_image(wiki_img, test_case["query"])
        status = "✅" if result == test_case["expected"] else "❌"
        
        print(f"{status} {test_case['title']}")
        print(f"   Query: {test_case['query']}")
        print(f"   Result: {result} (Expected: {test_case['expected']})")
        print(f"   Reason: {test_case['reason']}")
        print()


async def main():
    """Run comprehensive tests."""
    print("🚀 Comprehensive Vietnamese Language Support Test")
    print("=" * 70)
    print("Testing improvements for both Wikipedia and YouTube services")
    print()
    
    test_query_normalization_comprehensive()
    test_chemistry_detection_comprehensive()
    test_educational_content_scoring()
    test_language_prioritization()
    test_content_filtering()
    
    print("\n✅ All comprehensive tests completed!")
    print("\n📋 Summary of all improvements:")
    print("   🔍 Wikipedia Service:")
    print("     • Enhanced chemistry-focused image search")
    print("     • Improved relevance filtering")
    print("     • Better chemistry keyword detection")
    print("     • Prioritized chemistry-related content")
    print()
    print("   🎬 YouTube Service:")
    print("     • Added Vietnamese educational keywords")
    print("     • Enhanced educational channel detection")
    print("     • Improved video scoring for Vietnamese content")
    print("     • Added language-specific search parameters")
    print("     • Prioritized Vietnamese content for local users")
    print()
    print("   🌐 Overall:")
    print("     • Better query normalization and expansion")
    print("     • Improved chemistry content detection")
    print("     • Enhanced educational content filtering")
    print("     • Language-aware search strategies")


if __name__ == "__main__":
    asyncio.run(main())
