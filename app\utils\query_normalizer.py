"""Query normalization and i18n utilities."""

import re
import unicodedata
import structlog
from typing import List, Optional, Tuple

logger = structlog.get_logger()


class QueryNormalizer:
    """Handles query normalization and Vietnamese-English translation."""

    @classmethod
    def normalize_unicode(cls, text: str) -> str:
        """Normalize Unicode characters."""
        return unicodedata.normalize('NFC', text.strip())
    
    @classmethod
    def remove_diacritics(cls, text: str) -> str:
        """Remove Vietnamese diacritics."""
        # Mapping for Vietnamese characters
        vietnamese_map = {
            'à': 'a', 'á': 'a', 'ả': 'a', 'ã': 'a', 'ạ': 'a',
            'ă': 'a', 'ằ': 'a', 'ắ': 'a', 'ẳ': 'a', 'ẵ': 'a', 'ặ': 'a',
            'â': 'a', 'ầ': 'a', 'ấ': 'a', 'ẩ': 'a', 'ẫ': 'a', 'ậ': 'a',
            'è': 'e', 'é': 'e', 'ẻ': 'e', 'ẽ': 'e', 'ẹ': 'e',
            'ê': 'e', 'ề': 'e', 'ế': 'e', 'ể': 'e', 'ễ': 'e', 'ệ': 'e',
            'ì': 'i', 'í': 'i', 'ỉ': 'i', 'ĩ': 'i', 'ị': 'i',
            'ò': 'o', 'ó': 'o', 'ỏ': 'o', 'õ': 'o', 'ọ': 'o',
            'ô': 'o', 'ồ': 'o', 'ố': 'o', 'ổ': 'o', 'ỗ': 'o', 'ộ': 'o',
            'ơ': 'o', 'ờ': 'o', 'ớ': 'o', 'ở': 'o', 'ỡ': 'o', 'ợ': 'o',
            'ù': 'u', 'ú': 'u', 'ủ': 'u', 'ũ': 'u', 'ụ': 'u',
            'ư': 'u', 'ừ': 'u', 'ứ': 'u', 'ử': 'u', 'ữ': 'u', 'ự': 'u',
            'ỳ': 'y', 'ý': 'y', 'ỷ': 'y', 'ỹ': 'y', 'ỵ': 'y',
            'đ': 'd',
            # Uppercase
            'À': 'A', 'Á': 'A', 'Ả': 'A', 'Ã': 'A', 'Ạ': 'A',
            'Ă': 'A', 'Ằ': 'A', 'Ắ': 'A', 'Ẳ': 'A', 'Ẵ': 'A', 'Ặ': 'A',
            'Â': 'A', 'Ầ': 'A', 'Ấ': 'A', 'Ẩ': 'A', 'Ẫ': 'A', 'Ậ': 'A',
            'È': 'E', 'É': 'E', 'Ẻ': 'E', 'Ẽ': 'E', 'Ẹ': 'E',
            'Ê': 'E', 'Ề': 'E', 'Ế': 'E', 'Ể': 'E', 'Ễ': 'E', 'Ệ': 'E',
            'Ì': 'I', 'Í': 'I', 'Ỉ': 'I', 'Ĩ': 'I', 'Ị': 'I',
            'Ò': 'O', 'Ó': 'O', 'Ỏ': 'O', 'Õ': 'O', 'Ọ': 'O',
            'Ô': 'O', 'Ồ': 'O', 'Ố': 'O', 'Ổ': 'O', 'Ỗ': 'O', 'Ộ': 'O',
            'Ơ': 'O', 'Ờ': 'O', 'Ớ': 'O', 'Ở': 'O', 'Ỡ': 'O', 'Ợ': 'O',
            'Ù': 'U', 'Ú': 'U', 'Ủ': 'U', 'Ũ': 'U', 'Ụ': 'U',
            'Ư': 'U', 'Ừ': 'U', 'Ứ': 'U', 'Ử': 'U', 'Ữ': 'U', 'Ự': 'U',
            'Ỳ': 'Y', 'Ý': 'Y', 'Ỷ': 'Y', 'Ỹ': 'Y', 'Ỵ': 'Y',
            'Đ': 'D'
        }
        
        result = ""
        for char in text:
            result += vietnamese_map.get(char, char)
        return result
    
    @classmethod
    def is_chemical_formula(cls, text: str) -> bool:
        """Check if text looks like a chemical formula."""
        text = text.strip()

        # Enhanced pattern for chemical formulas (case insensitive)
        # Matches: H2, H2O, CaCl2, Ca(OH)2, CuSO4.5H2O, etc.
        formula_patterns = [
            r'^[A-Za-z][a-z]?(\d*[A-Za-z][a-z]?\d*)*$',  # Basic: H2SO4
            r'^[A-Za-z][a-z]?(\d*[A-Za-z][a-z]?\d*)*(\([A-Za-z][a-z]?\d*\)\d*)*$',  # With parentheses: Ca(OH)2
            r'^[A-Za-z][a-z]?(\d*[A-Za-z][a-z]?\d*)*(\.[A-Za-z][a-z]?\d*)*$',  # With hydrates: CuSO4.5H2O
        ]

        return any(re.match(pattern, text) for pattern in formula_patterns)


    

    
    @classmethod
    def normalize_query(cls, query: str) -> Tuple[str, List[str]]:
        """
        Basic normalization without API calls.

        Returns:
            Tuple of (primary_query, alternative_queries)
        """
        # Normalize Unicode
        query = cls.normalize_unicode(query)

        alternatives = []

        # Add version without diacritics as alternative
        no_diacritics = cls.remove_diacritics(query)
        if no_diacritics != query:
            alternatives.append(no_diacritics)

        return query, alternatives



    @classmethod
    async def normalize_query_with_apis(cls, query: str, chemspider_service=None, pubchem_service=None) -> Tuple[str, List[str]]:
        """
        Advanced normalization using multiple chemical APIs for unknown formulas.

        Args:
            query: The input query
            chemspider_service: ChemSpiderService instance (optional)
            pubchem_service: PubChemService instance (optional)

        Returns:
            Tuple of (primary_query, alternative_queries)
        """
        # First try basic normalization
        primary, alternatives = cls.normalize_query(query)

        # If it's a chemical formula, try to resolve via APIs
        if cls.is_chemical_formula(query):

            # Try both APIs in parallel for formula search
            pubchem_compound = None
            chemspider_compound = None

            # Launch both searches concurrently
            tasks = []
            if pubchem_service:
                tasks.append(("pubchem", pubchem_service.search_compound(query)))
            if chemspider_service:
                tasks.append(("chemspider", chemspider_service.search_compound(query)))

            if tasks:
                try:
                    # Wait for both APIs to complete
                    import asyncio
                    results = await asyncio.gather(*[task[1] for task in tasks], return_exceptions=True)

                    # Process results
                    for (source, result) in zip([task[0] for task in tasks], results):
                        if not isinstance(result, Exception) and result and result.title:
                            if source == "pubchem":
                                pubchem_compound = result
                                logger.info("Resolved formula via PubChem", formula=query, name=result.title)
                            elif source == "chemspider":
                                chemspider_compound = result
                                logger.info("Resolved formula via ChemSpider", formula=query, name=result.title)
                        elif isinstance(result, Exception):
                            logger.warning(f"{source.title()} formula search failed", formula=query, error=str(result))

                except Exception as e:
                    logger.warning("API formula search failed", formula=query, error=str(e))

            # Choose the best result
            chosen_compound = cls._choose_best_compound(pubchem_compound, chemspider_compound, query)

            # Use chosen result if found
            if chosen_compound and chosen_compound.title:
                primary = chosen_compound.title.lower()
                alternatives = [query] + alternatives

                # Add IUPAC name if different
                if chosen_compound.iupac_name and chosen_compound.iupac_name.lower() != primary:
                    alternatives.append(chosen_compound.iupac_name.lower())

                # Add alternative names from both sources
                if pubchem_compound and pubchem_compound != chosen_compound and pubchem_compound.title:
                    alt_name = pubchem_compound.title.lower()
                    if alt_name not in alternatives:
                        alternatives.append(alt_name)

                if chemspider_compound and chemspider_compound != chosen_compound and chemspider_compound.title:
                    alt_name = chemspider_compound.title.lower()
                    if alt_name not in alternatives:
                        alternatives.append(alt_name)

        return primary, alternatives

    @classmethod
    def _choose_best_compound(cls, pubchem_compound, chemspider_compound, original_query):
        """
        Choose the best compound result from multiple sources.

        Priority:
        1. ChemSpider (more curated, chemistry-focused)
        2. PubChem (larger database, but sometimes generic names)
        """
        # If only one source has results, use it
        if chemspider_compound and not pubchem_compound:
            return chemspider_compound
        if pubchem_compound and not chemspider_compound:
            return pubchem_compound

        # If both have results, prefer ChemSpider for chemistry-specific names
        if chemspider_compound and pubchem_compound:
            # Prefer ChemSpider if it has a more specific/chemical name
            cs_title = chemspider_compound.title.lower() if chemspider_compound.title else ""
            pc_title = pubchem_compound.title.lower() if pubchem_compound.title else ""

            # Prefer names that are not just the formula repeated
            if cs_title != original_query.lower() and pc_title == original_query.lower():
                return chemspider_compound
            elif pc_title != original_query.lower() and cs_title == original_query.lower():
                return pubchem_compound

            # Default to ChemSpider (more chemistry-focused curation)
            return chemspider_compound

        return None

    # Keep backward compatibility
    @classmethod
    async def normalize_query_with_chemspider(cls, query: str, chemspider_service=None) -> Tuple[str, List[str]]:
        """Backward compatibility method."""
        return await cls.normalize_query_with_apis(query, chemspider_service=chemspider_service)
    
    @classmethod
    def expand_query_for_search(cls, query: str, search_type: str = "general") -> List[str]:
        """
        Expand query for different search contexts.
        
        Args:
            query: The chemical name or formula
            search_type: "youtube", "wikipedia", or "general"
        """
        primary, alternatives = cls.normalize_query(query)
        
        if search_type == "youtube":
            # Add educational keywords for YouTube in both English and Vietnamese
            expanded = []
            all_queries = [primary] + alternatives

            # For chemical formulas, use normalized names if available
            if cls.is_chemical_formula(query):
                # Use primary and alternatives from normalization
                all_queries = [primary] + alternatives

            # English keywords
            english_keywords = [
                "chemistry", "experiment", "reaction", "properties", "molecule",
                "synthesis", "laboratory", "demo", "tutorial", "lesson"
            ]

            # Vietnamese keywords
            vietnamese_keywords = [
                "hóa học", "thí nghiệm", "phản ứng", "tính chất", "phân tử",
                "tổng hợp", "phòng thí nghiệm", "thực hành", "bài học", "giải thích"
            ]

            for q in all_queries[:3]:  # Limit base queries to avoid too many requests
                # Add English variations
                for keyword in english_keywords[:5]:  # Limit keywords
                    expanded.append(f"{q} {keyword}")

                # Add Vietnamese variations
                for keyword in vietnamese_keywords[:5]:  # Limit keywords
                    expanded.append(f"{q} {keyword}")

                # Add mixed language variations for common terms
                expanded.extend([
                    f"hóa học {q}",  # Vietnamese first
                    f"thí nghiệm {q}",
                    f"phản ứng {q}"
                ])

            return expanded[:20]  # Increased limit for more diverse results
        
        elif search_type == "wikipedia":
            # For Wikipedia, try exact matches and common variations
            return [primary] + alternatives
        
        else:
            return [primary] + alternatives
